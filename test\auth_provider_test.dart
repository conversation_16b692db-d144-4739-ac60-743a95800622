import 'package:flutter_test/flutter_test.dart';
import 'package:safarni/providers/auth_provider.dart';

void main() {
  group('AuthProvider Email Validation Tests', () {
    late AuthProvider authProvider;

    setUp(() {
      authProvider = AuthProvider();
    });

    test('Email validation should require email for login', () {
      // Email validation is now used only for login, so it should always require email

      // Empty email should be invalid
      expect(authProvider.validateEmail(''), isNotNull);
      expect(authProvider.validateEmail(null), isNotNull);

      // Valid email should be valid
      expect(authProvider.validateEmail('<EMAIL>'), isNull);

      // Invalid email format should be invalid
      expect(authProvider.validateEmail('invalid-email'), isNotNull);
    });

    test('Email validation should work for login purposes', () {
      // Valid email should be valid
      expect(authProvider.validateEmail('<EMAIL>'), isNull);

      // Invalid email format should be invalid
      expect(authProvider.validateEmail('invalid-email'), isNotNull);
    });

    test('Email validation should handle various email formats', () {
      // Valid emails
      expect(authProvider.validateEmail('<EMAIL>'), isNull);
      expect(authProvider.validateEmail('<EMAIL>'), isNull);
      expect(authProvider.validateEmail('<EMAIL>'), isNull);

      // Invalid emails
      expect(authProvider.validateEmail('user@'), isNotNull);
      expect(authProvider.validateEmail('@domain.com'), isNotNull);
      expect(authProvider.validateEmail('user.domain.com'), isNotNull);
      expect(authProvider.validateEmail('user@domain'), isNotNull);
    });

    test('Other validation methods should work correctly', () {
      // Test password validation
      expect(authProvider.validatePassword('123456'), isNull);
      expect(authProvider.validatePassword('12345'), isNotNull);
      expect(authProvider.validatePassword(''), isNotNull);
      expect(authProvider.validatePassword(null), isNotNull);

      // Test full name validation
      expect(authProvider.validateFullName('John Doe'), isNull);
      expect(authProvider.validateFullName('J'), isNotNull);
      expect(authProvider.validateFullName(''), isNotNull);
      expect(authProvider.validateFullName(null), isNotNull);

      // Test phone validation (optional)
      expect(authProvider.validatePhone(''), isNull);
      expect(authProvider.validatePhone(null), isNull);
      expect(authProvider.validatePhone('+212612345678'), isNull);
      expect(authProvider.validatePhone('**********'), isNull);
      expect(authProvider.validatePhone('invalid-phone'), isNotNull);
    });
  });
}
