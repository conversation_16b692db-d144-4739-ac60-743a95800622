import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/user_model.dart';
import '../models/trip_model.dart';

class SupabaseService {
  static final SupabaseClient _client = Supabase.instance.client;

  // Authentication methods
  static Future<Map<String, dynamic>> signUpWithPhone({
    required String phone,
    required String password,
    required String fullName,
  }) async {
    try {
      // Generate a unique dummy email for Supabase auth
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final dummyEmail = 'user_$<EMAIL>';

      // Use basic signup for web compatibility
      final response = await _client.auth.signUp(
        email: dummyEmail,
        password: password,
      );

      // If signup successful, create user profile
      if (response.user != null) {
        // Add a small delay to ensure user is properly created
        await Future.delayed(const Duration(milliseconds: 500));

        await _createUserProfile(
          userId: response.user!.id,
          phone: phone,
          fullName: fullName,
        );

        return {
          'success': true,
          'userId': response.user!.id,
        };
      } else {
        return {
          'success': false,
          'userId': null,
        };
      }
    } catch (e) {
      if (kDebugMode) {
        print('Signup error: $e');
        print('Error type: ${e.runtimeType}');
      }
      return {
        'success': false,
        'userId': null,
      };
    }
  }

  static Future<Map<String, dynamic>> signInWithPhone({
    required String phone,
    required String password,
  }) async {
    try {
      // First, find the user by phone number
      final userResponse = await _client
          .from('users')
          .select('id, email')
          .eq('phone', phone)
          .single();

      if (userResponse.isEmpty) {
        return {
          'success': false,
          'userId': null,
        };
      }

      // Get the email associated with this phone number
      final email = userResponse['email'] as String;

      // Sign in with the email and password
      final authResponse = await _client.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (authResponse.user != null) {
        return {
          'success': true,
          'userId': authResponse.user!.id,
        };
      } else {
        return {
          'success': false,
          'userId': null,
        };
      }
    } catch (e) {
      if (kDebugMode) {
        print('Signin error: $e');
        print('Error type: ${e.runtimeType}');
      }
      return {
        'success': false,
        'userId': null,
      };
    }
  }

  static Future<void> signOut() async {
    try {
      await _client.auth.signOut();
    } catch (e) {
      if (kDebugMode) {
        print('Signout error: $e');
      }
      rethrow;
    }
  }

  static Future<void> resetPassword(String email) async {
    try {
      await _client.auth.resetPasswordForEmail(email);
    } catch (e) {
      if (kDebugMode) {
        print('Reset password error: $e');
      }
      rethrow;
    }
  }

  // User profile methods
  static Future<void> _createUserProfile({
    required String userId,
    required String phone,
    required String fullName,
  }) async {
    try {
      // Generate a dummy email for database compatibility
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final dummyEmail = 'user_$<EMAIL>';

      await _client.from('users').insert({
        'id': userId,
        'email': dummyEmail,
        'full_name': fullName,
        'phone': phone,
        'role': 'traveler',
        'is_leader': false,
        'balance': 0.0,
        'is_verified': false,
        'rating': 0.0,
        'total_trips': 0,
        'total_ratings': 0,
        'badges': [],
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      if (kDebugMode) {
        print('Create user profile error: $e');
      }
      rethrow;
    }
  }

  static Future<UserModel?> getUserProfile(String userId) async {
    try {
      final response =
          await _client.from('users').select().eq('id', userId).single();

      return UserModel.fromJson(response);
    } catch (e) {
      if (kDebugMode) {
        print('Get user profile error: $e');
      }
      return null;
    }
  }

  static Future<void> updateUserProfile(UserModel user) async {
    try {
      await _client.from('users').update(user.toJson()).eq('id', user.id);
    } catch (e) {
      if (kDebugMode) {
        print('Update user profile error: $e');
      }
      rethrow;
    }
  }

  // Trip methods
  static Future<String> createTrip(TripModel trip) async {
    try {
      final response =
          await _client.from('trips').insert(trip.toJson()).select().single();

      return response['id'] as String;
    } catch (e) {
      if (kDebugMode) {
        print('Create trip error: $e');
      }
      rethrow;
    }
  }

  static Future<List<TripModel>> getTrips({
    String? leaderId,
    String? status,
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      var query = _client
          .from('trips')
          .select('*, leader:users!trips_leader_id_fkey(*)');

      if (leaderId != null) {
        query = query.eq('leader_id', leaderId);
      }

      if (status != null) {
        query = query.eq('status', status);
      }

      final response = await query
          .order('created_at', ascending: false)
          .range(offset, offset + limit - 1);

      return response
          .map<TripModel>((json) => TripModel.fromJson(json))
          .toList();
    } catch (e) {
      if (kDebugMode) {
        print('Get trips error: $e');
      }
      return [];
    }
  }

  static Future<TripModel?> getTrip(String tripId) async {
    try {
      final response = await _client
          .from('trips')
          .select('*, leader:users!trips_leader_id_fkey(*)')
          .eq('id', tripId)
          .single();

      return TripModel.fromJson(response);
    } catch (e) {
      if (kDebugMode) {
        print('Get trip error: $e');
      }
      return null;
    }
  }

  static Future<void> updateTrip(TripModel trip) async {
    try {
      await _client.from('trips').update(trip.toJson()).eq('id', trip.id);
    } catch (e) {
      if (kDebugMode) {
        print('Update trip error: $e');
      }
      rethrow;
    }
  }

  static Future<void> deleteTrip(String tripId) async {
    try {
      await _client.from('trips').delete().eq('id', tripId);
    } catch (e) {
      if (kDebugMode) {
        print('Delete trip error: $e');
      }
      rethrow;
    }
  }

  // Utility methods
  static User? get currentUser => _client.auth.currentUser;

  static Stream<AuthState> get authStateChanges =>
      _client.auth.onAuthStateChange;

  static Future<bool> isHealthy() async {
    try {
      // Simple health check - try to access the database
      await _client.from('users').select('id').limit(1);
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Health check failed: $e');
      }
      return false;
    }
  }

  /// Test Supabase connection specifically for web
  static Future<bool> testWebConnection() async {
    try {
      if (kDebugMode) {
        print('Testing Supabase connection...');
      }

      // Test basic connectivity
      final response = await _client.from('users').select('count').limit(1);

      if (kDebugMode) {
        print('Connection test successful: ${response.toString()}');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Connection test failed: $e');
        print('Error type: ${e.runtimeType}');
      }
      return false;
    }
  }
}
