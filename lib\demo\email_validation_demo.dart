import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../providers/auth_provider.dart';

/// Demo page to show email validation behavior in debug vs release mode
class EmailValidationDemo extends StatefulWidget {
  const EmailValidationDemo({super.key});

  @override
  State<EmailValidationDemo> createState() => _EmailValidationDemoState();
}

class _EmailValidationDemoState extends State<EmailValidationDemo> {
  final _emailController = TextEditingController();
  final _authProvider = AuthProvider();
  String? _validationResult;

  void _testValidation() {
    setState(() {
      _validationResult = _authProvider.validateEmail(_emailController.text);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Email Validation Demo'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Mode indicator
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: kReleaseMode ? Colors.red.shade100 : Colors.green.shade100,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: kReleaseMode ? Colors.red : Colors.green,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    kReleaseMode ? Icons.lock : Icons.bug_report,
                    color: kReleaseMode ? Colors.red : Colors.green,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    kReleaseMode ? 'Release Mode' : 'Debug Mode',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: kReleaseMode ? Colors.red : Colors.green,
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 20),
            
            // Explanation
            Text(
              'Email Validation Behavior:',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              kReleaseMode 
                ? '• In Release Mode: Email is REQUIRED and must be valid'
                : '• In Debug Mode: Email is OPTIONAL for development\n• Empty email is allowed\n• If provided, email must be valid format',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            
            const SizedBox(height: 30),
            
            // Email input
            TextField(
              controller: _emailController,
              decoration: InputDecoration(
                labelText: kReleaseMode 
                    ? 'البريد الإلكتروني' 
                    : 'البريد الإلكتروني (اختياري للتطوير)',
                border: const OutlineInputBorder(),
                prefixIcon: const Icon(Icons.email),
              ),
              keyboardType: TextInputType.emailAddress,
              onChanged: (_) => _testValidation(),
            ),
            
            const SizedBox(height: 16),
            
            // Test button
            ElevatedButton(
              onPressed: _testValidation,
              child: const Text('Test Validation'),
            ),
            
            const SizedBox(height: 20),
            
            // Validation result
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _validationResult == null 
                    ? Colors.green.shade100 
                    : Colors.red.shade100,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: _validationResult == null 
                      ? Colors.green 
                      : Colors.red,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Validation Result:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: _validationResult == null 
                          ? Colors.green.shade700 
                          : Colors.red.shade700,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _validationResult ?? 'Valid ✓',
                    style: TextStyle(
                      color: _validationResult == null 
                          ? Colors.green.shade700 
                          : Colors.red.shade700,
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 30),
            
            // Test cases
            Text(
              'Test Cases:',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            ...(_getTestCases().map((testCase) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 2),
              child: Row(
                children: [
                  Icon(
                    testCase['expected'] ? Icons.check : Icons.close,
                    color: testCase['expected'] ? Colors.green : Colors.red,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '${testCase['input']} → ${testCase['description']}',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ),
                ],
              ),
            ))),
          ],
        ),
      ),
    );
  }

  List<Map<String, dynamic>> _getTestCases() {
    if (kReleaseMode) {
      return [
        {'input': '(empty)', 'description': 'Should be invalid', 'expected': false},
        {'input': '<EMAIL>', 'description': 'Should be valid', 'expected': true},
        {'input': 'invalid-email', 'description': 'Should be invalid', 'expected': false},
      ];
    } else {
      return [
        {'input': '(empty)', 'description': 'Should be valid (optional)', 'expected': true},
        {'input': '<EMAIL>', 'description': 'Should be valid', 'expected': true},
        {'input': 'invalid-email', 'description': 'Should be invalid', 'expected': false},
      ];
    }
  }

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }
}
