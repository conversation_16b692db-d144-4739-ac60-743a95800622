import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/user_model.dart';
import '../services/supabase_service.dart';

class AuthProvider extends ChangeNotifier {
  UserModel? _currentUser;
  bool _isLoading = false;
  String? _error;

  // Getters
  UserModel? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isAuthenticated => _currentUser != null;
  bool get isTripLeader => _currentUser?.isTripLeader ?? false;
  bool get isTraveler => _currentUser?.isTraveler ?? false;

  AuthProvider() {
    _initializeAuth();
  }

  void _initializeAuth() {
    // Listen to auth state changes
    SupabaseService.authStateChanges.listen((data) {
      final user = data.session?.user;
      if (user != null) {
        _loadUserProfile(user.id);
      } else {
        _currentUser = null;
        notifyListeners();
      }
    });

    // Check if user is already logged in
    final currentUser = SupabaseService.currentUser;
    if (currentUser != null) {
      _loadUserProfile(currentUser.id);
    }
  }

  Future<void> _loadUserProfile(String userId) async {
    try {
      final userProfile = await SupabaseService.getUserProfile(userId);
      _currentUser = userProfile;
      _clearError();
      notifyListeners();
    } catch (e) {
      _setError('فشل في تحميل بيانات المستخدم');
      if (kDebugMode) {
        print('Load user profile error: $e');
      }
    }
  }

  Future<bool> signUp({
    String? email,
    required String password,
    required String fullName,
    required String role,
    String? phone,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      // In debug mode, generate a dummy email if none provided
      String finalEmail = email ?? '';
      if (!kReleaseMode && (email == null || email.isEmpty)) {
        // Generate a unique dummy email for development
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        finalEmail = 'dev_user_$<EMAIL>';
      }

      final response = await SupabaseService.signUp(
        email: finalEmail,
        password: password,
        fullName: fullName,
        phone: phone,
      );

      if (response.user != null) {
        await _loadUserProfile(response.user!.id);
        _setLoading(false);
        return true;
      } else {
        _setError('فشل في إنشاء الحساب');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('فشل في إنشاء الحساب: ${e.toString()}');
      _setLoading(false);
      if (kDebugMode) {
        print('Signup error: $e');
      }
      return false;
    }
  }

  Future<bool> signIn({
    String? email,
    required String password,
    String? userType,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      // In debug mode, use a dummy email if none provided
      String finalEmail = email ?? '';
      if (!kReleaseMode && (email == null || email.isEmpty)) {
        // For development, we can't really sign in without an email
        // since we need to match an existing account
        _setError('في وضع التطوير، يجب إدخال البريد الإلكتروني للدخول');
        _setLoading(false);
        return false;
      }

      final response = await SupabaseService.signIn(
        email: finalEmail,
        password: password,
      );

      if (response.user != null) {
        await _loadUserProfile(response.user!.id);
        _setLoading(false);
        return true;
      } else {
        _setError('فشل في تسجيل الدخول');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('فشل في تسجيل الدخول: ${e.toString()}');
      _setLoading(false);
      if (kDebugMode) {
        print('Signin error: $e');
      }
      return false;
    }
  }

  Future<void> signOut() async {
    _setLoading(true);
    _clearError();

    try {
      await SupabaseService.signOut();
      _currentUser = null;
      _setLoading(false);
    } catch (e) {
      _setError('فشل في تسجيل الخروج');
      _setLoading(false);
      if (kDebugMode) {
        print('Signout error: $e');
      }
    }
  }

  Future<bool> resetPassword(String email) async {
    _setLoading(true);
    _clearError();

    try {
      await SupabaseService.resetPassword(email);
      _setLoading(false);
      return true;
    } catch (e) {
      _setError('فشل في إرسال رابط إعادة تعيين كلمة المرور');
      _setLoading(false);
      if (kDebugMode) {
        print('Reset password error: $e');
      }
      return false;
    }
  }

  Future<bool> updateProfile(UserModel updatedUser) async {
    _setLoading(true);
    _clearError();

    try {
      await SupabaseService.updateUserProfile(updatedUser);
      _currentUser = updatedUser;
      _setLoading(false);
      return true;
    } catch (e) {
      _setError('فشل في تحديث الملف الشخصي');
      _setLoading(false);
      if (kDebugMode) {
        print('Update profile error: $e');
      }
      return false;
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  void clearError() {
    _clearError();
  }

  // Validation helpers
  bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  bool isValidPassword(String password) {
    return password.length >= 6;
  }

  bool isValidMoroccanPhone(String phone) {
    final cleanPhone = phone.replaceAll(RegExp(r'[\s\-\(\)]'), '');
    return RegExp(r'^(\+212|0)(6|7)[0-9]{8}$').hasMatch(cleanPhone);
  }

  String? validateEmail(String? email) {
    // In debug mode, email is optional
    if (!kReleaseMode) {
      if (email != null && email.isNotEmpty && !isValidEmail(email)) {
        return 'البريد الإلكتروني غير صحيح';
      }
      return null;
    }

    // In release mode, email is required
    if (email == null || email.isEmpty) {
      return 'البريد الإلكتروني مطلوب';
    }
    if (!isValidEmail(email)) {
      return 'البريد الإلكتروني غير صحيح';
    }
    return null;
  }

  String? validatePassword(String? password) {
    if (password == null || password.isEmpty) {
      return 'كلمة المرور مطلوبة';
    }
    if (!isValidPassword(password)) {
      return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    }
    return null;
  }

  String? validateFullName(String? fullName) {
    if (fullName == null || fullName.isEmpty) {
      return 'الاسم الكامل مطلوب';
    }
    if (fullName.length < 2) {
      return 'الاسم يجب أن يكون حرفين على الأقل';
    }
    return null;
  }

  String? validatePhone(String? phone) {
    if (phone != null && phone.isNotEmpty && !isValidMoroccanPhone(phone)) {
      return 'رقم الهاتف غير صحيح';
    }
    return null;
  }

  String? validateConfirmPassword(String? password, String? confirmPassword) {
    if (confirmPassword == null || confirmPassword.isEmpty) {
      return 'تأكيد كلمة المرور مطلوب';
    }
    if (password != confirmPassword) {
      return 'كلمة المرور غير متطابقة';
    }
    return null;
  }
}
